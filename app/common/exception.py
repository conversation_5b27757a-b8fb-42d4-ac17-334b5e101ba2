from builtins import Exception

__all__ = ["CloudAuditException", "InternalServerException", "BadRequestException", "ResourceNotFoundException",
           "InvalidEmailException", "InvalidPasswordException", "UserExistsException", "InvalidCredentialsException",
           "AccountExistsException", "InvalidAWSCredentialsException", "InvalidGCPCredentialsException",
           "InvalidAzureCredentialsException", "MaxScanAccountsException", "NoServicesAvailableForScanException",
           "InvalidCloudProviderNameException", "ScanAlreadyRunningException", "AWSChildAccountAccessRoleException",
           "InsufficientPermissionsException", "InvalidPermissionException", "CustomRoleNotFoundException",
           "WorkspaceNameRequiredException", "InvalidAWSRegionException", "NoAWSRegionException",
           "ServiceRequiredException", "ServiceNotValidException", "ScanNotFoundException", "ScanNotAuthorizedException",
           "ResourceNotFoundOrInsufficientPermissionsException", "RemediationInProgressException",
           "RemediateDetailNotValidException", "AdminUserNotDeletedException", "CurrentPasswordNotValidException",
           "AdminUserPasswordNotChangedException", "AdminUserInfoNotUpdatedException", "CannotUpdateYourselfException",
           "NotWorkspaceOwnerException", "CannotTransferToSelfException", "TargetUserNotInWorkspaceException",
           "TargetUserNotAdminException"]


class CloudAuditException(Exception):
    def __init__(self, message: str = None, status_code: int = None):
        self.message = message
        self.status_code = status_code


class InternalServerException(CloudAuditException):
    pass


class BadRequestException(CloudAuditException):
    pass


class ResourceNotFoundException(CloudAuditException):
    pass


class InvalidEmailException(CloudAuditException):
    pass


class InvalidPasswordException(CloudAuditException):
    pass


class UserExistsException(CloudAuditException):
    pass


class AdminUserNotDeletedException(CloudAuditException):
    pass


class InvalidCredentialsException(CloudAuditException):
    pass


class AccountExistsException(CloudAuditException):
    pass


class InvalidAWSCredentialsException(CloudAuditException):
    pass


class InvalidGCPCredentialsException(CloudAuditException):
    pass


class InvalidAzureCredentialsException(CloudAuditException):
    pass


class MaxScanAccountsException(CloudAuditException):
    pass


class ServiceRequiredException(CloudAuditException):
    pass


class NoServicesAvailableForScanException(CloudAuditException):
    pass


class ServiceNotValidException(CloudAuditException):
    pass


class ScanNotFoundException(CloudAuditException):
    pass


class ScanNotAuthorizedException(CloudAuditException):
    pass


class InvalidCloudProviderNameException(CloudAuditException):
    pass


class NoAWSRegionException(CloudAuditException):
    pass


class InvalidAWSRegionException(CloudAuditException):
    pass


class ScanAlreadyRunningException(CloudAuditException):
    pass


class AWSChildAccountAccessRoleException(CloudAuditException):
    pass


class InsufficientPermissionsException(CloudAuditException):
    pass


class ResourceNotFoundOrInsufficientPermissionsException(CloudAuditException):
    pass


class InvalidPermissionException(CloudAuditException):
    pass


class CustomRoleNotFoundException(CloudAuditException):
    pass


class WorkspaceNameRequiredException(CloudAuditException):
    pass


class RemediationInProgressException(CloudAuditException):
    """Exception raised when a remediation is already in progress."""
    pass


class RemediateDetailNotValidException(CloudAuditException):
    """Exception raised when a remediation detail is not found."""
    pass


class CurrentPasswordNotValidException(CloudAuditException):
    """Exception raised when a current password is not valid."""
    pass


class AdminUserPasswordNotChangedException(CloudAuditException):
    """Exception raised when an admin user tries to change password."""
    pass


class AdminUserInfoNotUpdatedException(CloudAuditException):
    """Exception raised when an admin user tries to update info."""
    pass


class CannotUpdateYourselfException(CloudAuditException):
    """Exception raised when a user tries to update themselves."""
    pass


class NotWorkspaceOwnerException(CloudAuditException):
    """Exception raised when a non-owner tries to transfer workspace ownership."""
    pass


class CannotTransferToSelfException(CloudAuditException):
    """Exception raised when trying to transfer ownership to yourself."""
    pass


class TargetUserNotInWorkspaceException(CloudAuditException):
    """Exception raised when target user is not in the same workspace."""
    pass


class TargetUserNotAdminException(CloudAuditException):
    """Exception raised when target user doesn't have admin privileges."""
    pass
