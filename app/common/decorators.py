import time
import jwt
from functools import wraps
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .exception import InvalidCredentialsException

__all__ = ['get_current_user', 'requires_permission', 'timeit']

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")


async def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        from app import app
        payload: dict = jwt.decode(token, app.config.JWT_SECRET_KEY, algorithms=[app.config.ENCRYPTION_ALGORITHM])
        email: str = payload.get('sub')
        user_id: int = payload.get('user_id')
        workspace_id: int = payload.get('workspace_id')
        if not email or not user_id or not workspace_id:
            raise InvalidCredentialsException
        return {"email": email, "user_id": user_id, "workspace_id": workspace_id}
    except Exception as e:
        raise InvalidCredentialsException


def requires_permission(permission: str):
    async def dependency(user: dict = Depends(get_current_user)):
        from app import app
        from app.core.models.mysql import fetch_user_permissions

        user_id = user["user_id"]
        user_permissions = await fetch_user_permissions(app.state.connection_pool, user_id)

        if permission not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "ok": False,
                    "status_code": status.HTTP_403_FORBIDDEN,
                    "message": f"You do not have the required permission: {permission}",
                },
            )
        user.update({"permissions": user_permissions})
        return user

    return Depends(dependency)


def timeit(method):
    async def timed(*args, **kw):
        ts = time.time()
        result = await method(*args, **kw)
        te = time.time()
        from app import app
        app.logger.info('%r  %2.2f ms' % (method.__name__, (te - ts) * 1000))
        return result

    return timed
