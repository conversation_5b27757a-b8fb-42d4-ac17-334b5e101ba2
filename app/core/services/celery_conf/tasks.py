import importlib
import boto3
from celery.utils.log import get_task_logger
from botocore.exceptions import ClientError
from .celery import celery_obj
from app.core.models.mysql import (get_account_credentials, save_findings, check_scan_completion,
                                   update_scan, update_scan_service,
                                   get_findings_for_scan_service_policy_check, update_findings,
                                   mysql_connection_pool_factory, sync_scan_services)
from app.common.enums import ScanStatusEnum, ScanServiceStatusEnum


__all__ = ['get_findings', 'scan_service_task', 'RegionAwareServiceScanner']


logger = get_task_logger(__name__)


class RegionAwareServiceScanner:
    """
    Region-aware service scanner that uses AWS Resource Explorer to determine
    if a service has resources in a specific region before scanning.
    """

    def __init__(self, account_id: str, region: str, credentials: dict = None):
        self.account_id = account_id
        self.region = region
        self.credentials = credentials
        self._resource_explorer_client = None
        self._resource_explorer_enabled = None

    def _get_resource_explorer_client(self):
        """Get or create Resource Explorer client."""
        if self._resource_explorer_client is None:
            if self.credentials:
                # Use provided credentials
                session = boto3.Session(
                    aws_access_key_id=self.credentials.get('access_key'),
                    aws_secret_access_key=self.credentials.get('secret_key'),
                    aws_session_token=self.credentials.get('session_token'),
                    region_name=self.region
                )
                self._resource_explorer_client = session.client("resource-explorer-2")
            else:
                # Use default credentials
                self._resource_explorer_client = boto3.client("resource-explorer-2", region_name=self.region)
        return self._resource_explorer_client

    def is_resource_explorer_enabled(self) -> bool:
        """Check if AWS Resource Explorer is enabled in the account/region."""
        if self._resource_explorer_enabled is not None:
            return self._resource_explorer_enabled

        try:
            client = self._get_resource_explorer_client()
            indexes = client.list_indexes()
            self._resource_explorer_enabled = len(indexes.get("Indexes", [])) > 0

            if self._resource_explorer_enabled:
                logger.info(f"✅ Resource Explorer enabled in account {self.account_id}")
            else:
                logger.info(f"⚠️  Resource Explorer not enabled in account {self.account_id}")

            return self._resource_explorer_enabled

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "")
            if error_code in ["AccessDeniedException", "ResourceNotFoundException", "ValidationException"]:
                logger.info(f"⚠️  Resource Explorer not enabled in account {self.account_id} (Error: {error_code})")
                self._resource_explorer_enabled = False
                return False
            else:
                logger.warning(f"❌ Error checking Resource Explorer status: {e}")
                self._resource_explorer_enabled = False
                return False
        except Exception as e:
            logger.warning(f"❌ Unexpected error checking Resource Explorer: {e}")
            self._resource_explorer_enabled = False
            return False

    def service_exists_in_region(self, service_name: str) -> bool:
        """Use AWS Resource Explorer to check if a service has resources in the region."""
        try:
            client = self._get_resource_explorer_client()
            query = f"service:{service_name} region:{self.region}"
            response = client.search(QueryString=query, MaxResults=5)
            resources = response.get("Resources", [])

            exists = len(resources) > 0
            logger.debug(f"🔍 Resource Explorer query for {service_name} in {self.region}: {len(resources)} resources found")
            return exists

        except ClientError as e:
            logger.error(f"❌ Failed to query Resource Explorer for {service_name} in {self.region}: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error querying Resource Explorer: {e}")
            return False

    def should_scan(self, service_name: str) -> bool:
        """Decide whether to scan a service in this region."""
        if not self.is_resource_explorer_enabled():
            logger.info(f"ℹ️  Resource Explorer not enabled in {self.region}, falling back to default scanning.")
            return True  # fallback to scanning all regions

        exists = self.service_exists_in_region(service_name)
        if exists:
            logger.info(f"✅ Service '{service_name}' exists in {self.region}, proceeding with scan.")
            return True
        else:
            logger.info(f"⏭️  Service '{service_name}' NOT found in {self.region}, skipping scan.")
            return False


async def get_findings(credentials, cloud_provider_name, service_name, regions, account_id=None):
    """
    Get findings for a specific service with region-aware optimization using AWS Resource Explorer.
    """
    logger.info(f"🚀 Starting {service_name} scan for account {account_id} across {len(regions)} regions")

    # Initialize region filtering
    optimized_regions = []
    skipped_regions = []
    resource_explorer_enabled = False

    # Check Resource Explorer availability (use first region for account-level check)
    if regions and cloud_provider_name == 'aws':
        try:
            scanner = RegionAwareServiceScanner(account_id, regions[0], credentials)
            resource_explorer_enabled = scanner.is_resource_explorer_enabled()

            if resource_explorer_enabled:
                logger.info(f"✅ Resource Explorer enabled in account {account_id}")
            else:
                logger.info(f"⚠️  Resource Explorer not enabled in account {account_id}")

        except Exception as e:
            logger.warning(f"❌ Error checking Resource Explorer status: {e}")
            resource_explorer_enabled = False

    # Filter regions based on resource availability
    if resource_explorer_enabled and cloud_provider_name == 'aws':
        logger.info(f"🔍 Using Resource Explorer to filter regions for {service_name}")

        for region in regions:
            try:
                scanner = RegionAwareServiceScanner(account_id, region, credentials)
                should_scan = scanner.should_scan(service_name)

                if should_scan:
                    optimized_regions.append(region)
                    logger.info(f"✅ Service '{service_name}' exists in {region}, proceeding with scan.")
                else:
                    skipped_regions.append(region)
                    logger.info(f"⏭️  Service '{service_name}' NOT found in {region}, skipping scan.")

            except Exception as e:
                logger.warning(f"❌ Error checking {service_name} in {region}: {e}")
                # On error, include region to be safe
                optimized_regions.append(region)
                logger.info(f"🔄 Including {region} due to check error (fail-safe)")
    else:
        # Fallback: scan all regions
        optimized_regions = regions
        if cloud_provider_name == 'aws':
            logger.info(f"🔄 Resource Explorer not available, scanning all regions for {service_name}")
        else:
            logger.info(f"ℹ️  Non-AWS provider, scanning all regions for {service_name}")

    # Log optimization summary
    if skipped_regions:
        savings_percent = (len(skipped_regions) / len(regions)) * 100
        logger.info(f"📊 Region optimization summary for {service_name}:")
        logger.info(f"   • Total regions: {len(regions)}")
        logger.info(f"   • Regions to scan: {len(optimized_regions)} - {optimized_regions}")
        logger.info(f"   • Skipped regions: {len(skipped_regions)} - {skipped_regions}")
        logger.info(f"   • Time savings: {savings_percent:.1f}%")
    else:
        logger.info(f"ℹ️  Scanning all {len(optimized_regions)} regions for {service_name}")

    # Proceed with scanning only optimized regions
    if not optimized_regions:
        logger.info(f"⏭️  No regions to scan for {service_name}, returning empty findings")
        return {}

    # Import and instantiate service class
    module = importlib.import_module(f'app.core.services.api_cloud_providers.{cloud_provider_name}.'
                                     f'{service_name}.scan')
    service_class = getattr(module, 'ChecksProcessor')

    # Create service instance with optimized regions
    service_instance = service_class(credentials, optimized_regions)

    # Set account_id if the service instance supports it (for caching)
    if hasattr(service_instance, 'account_id'):
        service_instance.account_id = account_id

    # Run checks on optimized regions
    logger.info(f"🔍 Running {service_name} checks on {len(optimized_regions)} optimized regions")
    findings = await service_instance.run_checks()

    logger.info(f"✅ Completed {service_name} scan for account {account_id}")
    return findings


async def save_or_update_findings(mysql_pool, scan_id, service_id, findings):
    for policy_check, finding_detail in findings.items():
        # check if finding already exists
        result = await get_findings_for_scan_service_policy_check(mysql_pool, scan_id, service_id, policy_check)
        if result:
            # update existing finding
            await update_findings(mysql_pool, result['id'], finding_detail)
        else:
            # insert new finding
            await save_findings(mysql_pool, scan_id, service_id, policy_check, finding_detail)


@celery_obj.task
def scan_service_task(scan_id, service, payload):
    import asyncio
    loop = asyncio.get_event_loop()
    # loop = asyncio.new_event_loop()
    # asyncio.set_event_loop(loop)

    async def run_task():
        # if mysql_pool is None:
        #     await init_mysql_pool()

        mysql_pool = await mysql_connection_pool_factory()

        service_id = service["id"]
        service_name = service["name"]
        scan_service_id = await sync_scan_services(mysql_pool, scan_id, service_id, ScanServiceStatusEnum.RUNNING.value)

        credentials = await get_account_credentials(mysql_pool, payload["account_id"])

        # Add AWS account ID to credentials for optimization and Resource Explorer
        if 'aws_account_id' not in credentials:
            credentials['aws_account_id'] = payload.get("account_id")

        # Ensure credentials have the right format for boto3
        if payload["cloud_provider_name"] == "aws":
            # Map credential keys to boto3 expected format
            if 'access_key' not in credentials and 'aws_access_key_id' in credentials:
                credentials['access_key'] = credentials['aws_access_key_id']
            if 'secret_key' not in credentials and 'aws_secret_access_key' in credentials:
                credentials['secret_key'] = credentials['aws_secret_access_key']
            if 'session_token' not in credentials and 'aws_session_token' in credentials:
                credentials['session_token'] = credentials['aws_session_token']

        logger.info(f"🔍 Starting region-aware scan for {service_name} in account {payload['account_id']}")

        findings = await get_findings(
            credentials,
            payload["cloud_provider_name"],
            service_name,
            payload["regions"],
            payload.get("account_id")
        )

        await save_or_update_findings(mysql_pool, scan_id, service_id, findings)

        # Log completion with optimization summary
        logger.info(f"✅ Scan service task completed for service: {service_name}")
        logger.info(f"📊 Final summary: {len(findings)} findings collected for {service_name}")

        # update each scan service status to completed
        await update_scan_service(conn_pool=mysql_pool, scan_service_id=scan_service_id, update_last_scanned_at=True)
        # mark parent scan as completed if all services are done
        is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
        if is_scan_completed["status_check"]:
            await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)

    result = loop.run_until_complete(run_task())
    # scan_service_task.request.acknowledge()
    return result
