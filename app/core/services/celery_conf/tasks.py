import importlib
from celery.utils.log import get_task_logger
from .celery import celery_obj
from app.core.models.mysql import (get_account_credentials, save_findings, check_scan_completion,
                                   update_scan, update_scan_service,
                                   get_findings_for_scan_service_policy_check, update_findings,
                                   mysql_connection_pool_factory, sync_scan_services)
from app.common.enums import ScanStatusEnum, ScanServiceStatusEnum


__all__ = ['get_findings', 'scan_service_task']


logger = get_task_logger(__name__)


async def get_findings(credentials, cloud_provider_name, service_name, regions, account_id=None):
    """
    Get findings for a specific service with region-aware optimization.
    The optimization is handled within the service classes using the new base scanning methods.
    """
    logger.info(f"Starting {service_name} scan for account {account_id} across {len(regions)} regions")

    module = importlib.import_module(f'app.core.services.api_cloud_providers.{cloud_provider_name}.'
                                     f'{service_name}.scan')
    service_class = getattr(module, 'ChecksProcessor')

    # Create service instance with credentials and regions
    service_instance = service_class(credentials, regions)

    # Set account_id if the service instance supports it (for caching)
    if hasattr(service_instance, 'account_id'):
        service_instance.account_id = account_id

    # Run checks with built-in region optimization
    findings = await service_instance.run_checks()

    logger.info(f"Completed {service_name} scan for account {account_id}")
    return findings


async def save_or_update_findings(mysql_pool, scan_id, service_id, findings):
    for policy_check, finding_detail in findings.items():
        # check if finding already exists
        result = await get_findings_for_scan_service_policy_check(mysql_pool, scan_id, service_id, policy_check)
        if result:
            # update existing finding
            await update_findings(mysql_pool, result['id'], finding_detail)
        else:
            # insert new finding
            await save_findings(mysql_pool, scan_id, service_id, policy_check, finding_detail)


@celery_obj.task
def scan_service_task(scan_id, service, payload):
    import asyncio
    loop = asyncio.get_event_loop()
    # loop = asyncio.new_event_loop()
    # asyncio.set_event_loop(loop)

    async def run_task():
        # if mysql_pool is None:
        #     await init_mysql_pool()

        mysql_pool = await mysql_connection_pool_factory()

        service_id = service["id"]
        service_name = service["name"]
        scan_service_id = await sync_scan_services(mysql_pool, scan_id, service_id, ScanServiceStatusEnum.RUNNING.value)

        credentials = await get_account_credentials(mysql_pool, payload["account_id"])

        # Add AWS account ID to credentials for optimization
        if 'aws_account_id' not in credentials:
            credentials['aws_account_id'] = payload.get("account_id")

        findings = await get_findings(
            credentials,
            payload["cloud_provider_name"],
            service_name,
            payload["regions"],
            payload.get("account_id")
        )

        await save_or_update_findings(mysql_pool, scan_id, service_id, findings)

        # update each scan service status to completed
        await update_scan_service(conn_pool=mysql_pool, scan_service_id=scan_service_id, update_last_scanned_at=True)
        # mark parent scan as completed if all services are done
        is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
        if is_scan_completed["status_check"]:
            await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)

    result = loop.run_until_complete(run_task())
    # scan_service_task.request.acknowledge()
    return result
