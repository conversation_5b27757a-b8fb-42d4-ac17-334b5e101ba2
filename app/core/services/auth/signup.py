from app import app
from app.core.models.mysql import (get_user_data, add_new_user, add_workspace, update_workspace_user, update_user_admin,
                                   add_user_role)
from app.common import (UserExistsException, InternalServerException, get_encrypted_password, get_utc_timestamp,
                        PredefinedUserRoleEnum, WorkspaceNameRequiredException)
from app.common.slack import send_slack_message

__all__ = ['SignUpService']


class SignUpService:
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool

    async def process(self):
        user = await get_user_data(self.conn_pool, self.message)
        if user and user.get("email") and user["email"].lower() == self.message.email.lower():
            raise UserExistsException

        password_hash = get_encrypted_password(self.message.password)

        try:
            if not self.message.workspace_name:
                raise WorkspaceNameRequiredException
            workspace_id = await add_workspace(self.conn_pool, self.message.workspace_name, get_utc_timestamp())
            user_id = await add_new_user(
                self.conn_pool,
                self.message.email,
                password_hash,
                workspace_id,
                get_utc_timestamp(),
                None,  # created_by is None for initial signup
                self.message.first_name,
                self.message.last_name
            )
            await update_workspace_user(self.conn_pool, user_id, workspace_id)
            await update_user_admin(self.conn_pool, user_id)
            await add_user_role(self.conn_pool, user_id, PredefinedUserRoleEnum.OWNER.value)

        except Exception:
            raise InternalServerException

        try:
            # Format username from first_name and last_name
            username = ""
            if self.message.first_name or self.message.last_name:
                first_name = self.message.first_name or ""
                last_name = self.message.last_name or ""
                username = f"{first_name} {last_name}".strip()
            else:
                username = self.message.email

            await send_slack_message(
                f"`{self.message.workspace_name}` workspace created by `{username}`."
            )
        except Exception as slack_error:
            app.logger.warning(f"Failed to send Slack notification for user signup: {slack_error}")
