import asyncio
from typing import Dict, List, Set, Optional
from celery.utils.log import get_task_logger
from botocore.exceptions import ClientError, NoCredentialsError
from app.common.enums import AWSServiceNameEnum, AWSRegionNameEnum

__all__ = ['DirectServiceChecker']

logger = get_task_logger(__name__)


class DirectServiceChecker:
    """
    Direct service checker for discovering service-region mappings without Resource Explorer.
    This class uses direct service API calls to determine which services have resources
    in which regions, providing a fallback when Resource Explorer is unavailable.
    """
    
    def __init__(self, session, timeout_seconds: int = 10):
        """
        Initialize Direct Service Checker.
        
        Args:
            session: AWS session object
            timeout_seconds: Timeout for each service check (default: 10 seconds)
        """
        self.session = session
        self.timeout_seconds = timeout_seconds
    
    async def get_service_regions_mapping(self, services: List[str], regions: List[str], account_id: str = None) -> Dict[str, List[str]]:
        """
        Get mapping of services to regions where they have resources using direct API calls.

        Args:
            services: List of service names to check
            regions: List of regions to check
            account_id: AWS account ID (for logging)

        Returns:
            Dict mapping service names to list of regions where they have resources
        """
        logger.info(f"🔍 Starting direct service discovery for account {account_id or 'unknown'}")
        logger.info(f"   • Services: {services}")
        logger.info(f"   • Regions: {regions}")

        service_region_mapping = {}

        # Process each service to find regions with resources
        for service in services:
            try:
                service_regions = await self._get_regions_for_service(service, regions)
                service_region_mapping[service] = service_regions

                if len(service_regions) < len(regions):
                    skipped_regions = set(regions) - set(service_regions)
                    savings_percent = ((len(regions) - len(service_regions)) / len(regions)) * 100
                    logger.info(f"✅ {service}: found in {len(service_regions)}/{len(regions)} regions")
                    logger.info(f"   • Active regions: {service_regions}")
                    logger.info(f"   • Skipped regions: {list(skipped_regions)}")
                    logger.info(f"   • Time savings: {savings_percent:.1f}%")
                else:
                    logger.info(f"ℹ️  {service}: found in all {len(regions)} regions")

            except Exception as e:
                logger.error(f"❌ Error checking service '{service}': {str(e)}")
                # Fallback to all regions if service check fails
                service_region_mapping[service] = regions
                logger.warning(f"   • Fallback: scanning all regions for {service}")

        return service_region_mapping
    
    async def _get_regions_for_service(self, service: str, regions: List[str]) -> List[str]:
        """
        Get regions where a specific service has resources using direct API calls.

        Args:
            service: Service name
            regions: List of regions to check

        Returns:
            List of regions where the service has resources
        """
        regions_with_resources = []

        logger.info(f"🔍 Checking {service} across {len(regions)} regions...")

        # Check each region sequentially to avoid overwhelming APIs
        for region in regions:
            try:
                logger.debug(f"   • Checking {service} in {region}...")
                has_resources = await self._check_service_in_region(service, region)
                if has_resources:
                    regions_with_resources.append(region)
                    logger.info(f"   ✅ {service} has resources in {region}")
                else:
                    logger.info(f"   ❌ {service} has no resources in {region}")
            except Exception as e:
                logger.warning(f"   ⚠️  Error checking {service} in {region}: {str(e)}")
                # Include region in case of error to be safe (conservative approach)
                regions_with_resources.append(region)

        # If no regions found, return empty list (don't scan any regions)
        if not regions_with_resources:
            logger.info(f"✅ No {service} resources found in any region - will skip all {service} scans")
            return []

        return regions_with_resources
    
    async def _check_service_in_region(self, service: str, region: str) -> bool:
        """
        Check if a service has resources in a specific region using direct API calls.

        Args:
            service: Service name
            region: Region to check

        Returns:
            True if service has resources in the region, False otherwise
        """
        try:
            # Map service names to AWS service client names
            service_mapping = {
                'ec2': 'ec2',
                'rds': 'rds',
                'lambda': 'lambda',
                'ecs': 'ecs',
                'eks': 'eks',
                'elb': 'elb',
                'efs': 'efs',
                'elasticache': 'elasticache',
                's3': 's3',
                'iam': 'iam'
            }

            if service not in service_mapping:
                logger.warning(f"Unknown service '{service}' in direct check, assuming no resources")
                return False

            service_client_name = service_mapping[service]

            # Use timeout to prevent hanging
            async def check_service():
                async with self.session.client(service_client_name, region_name=region) as client:
                    # Use service-specific methods to check for resources
                    if service == 'ec2':
                        response = await client.describe_instances(MaxResults=1)
                        has_instances = len(response.get('Reservations', [])) > 0
                        logger.debug(f"   • EC2 instances in {region}: {has_instances}")
                        return has_instances

                    elif service == 'rds':
                        # Check for DB instances first (most common)
                        try:
                            response = await client.describe_db_instances(MaxRecords=1)
                            db_instances = response.get('DBInstances', [])
                            if len(db_instances) > 0:
                                logger.debug(f"   • RDS DB instances in {region}: {len(db_instances)}")
                                return True
                        except ClientError as e:
                            if e.response['Error']['Code'] not in ['AccessDenied', 'UnauthorizedOperation']:
                                logger.debug(f"   • Error checking RDS instances in {region}: {e.response['Error']['Code']}")

                        # Also check for DB clusters
                        try:
                            response = await client.describe_db_clusters(MaxRecords=1)
                            db_clusters = response.get('DBClusters', [])
                            if len(db_clusters) > 0:
                                logger.debug(f"   • RDS DB clusters in {region}: {len(db_clusters)}")
                                return True
                        except ClientError as e:
                            if e.response['Error']['Code'] not in ['AccessDenied', 'UnauthorizedOperation']:
                                logger.debug(f"   • Error checking RDS clusters in {region}: {e.response['Error']['Code']}")

                        logger.debug(f"   • No RDS resources found in {region}")
                        return False

                    elif service == 'lambda':
                        response = await client.list_functions(MaxItems=1)
                        has_functions = len(response.get('Functions', [])) > 0
                        logger.debug(f"   • Lambda functions in {region}: {has_functions}")
                        return has_functions

                    elif service == 'ecs':
                        response = await client.list_clusters(maxResults=1)
                        has_clusters = len(response.get('clusterArns', [])) > 0
                        logger.debug(f"   • ECS clusters in {region}: {has_clusters}")
                        return has_clusters

                    elif service == 'eks':
                        response = await client.list_clusters(maxResults=1)
                        has_clusters = len(response.get('clusters', [])) > 0
                        logger.debug(f"   • EKS clusters in {region}: {has_clusters}")
                        return has_clusters

                    elif service == 'elb':
                        has_elb = False
                        # Check classic load balancers
                        try:
                            response = await client.describe_load_balancers(PageSize=1)
                            if len(response.get('LoadBalancerDescriptions', [])) > 0:
                                has_elb = True
                        except ClientError:
                            pass

                        # Check ALB/NLB using ELBv2 client
                        if not has_elb:
                            try:
                                async with self.session.client('elbv2', region_name=region) as elbv2_client:
                                    response = await elbv2_client.describe_load_balancers(PageSize=1)
                                    has_elb = len(response.get('LoadBalancers', [])) > 0
                            except ClientError:
                                pass

                        logger.debug(f"   • ELB resources in {region}: {has_elb}")
                        return has_elb

                    elif service == 'efs':
                        response = await client.describe_file_systems(MaxItems=1)
                        has_filesystems = len(response.get('FileSystems', [])) > 0
                        logger.debug(f"   • EFS file systems in {region}: {has_filesystems}")
                        return has_filesystems

                    elif service == 'elasticache':
                        has_cache = False
                        # Check cache clusters
                        try:
                            response = await client.describe_cache_clusters(MaxRecords=1)
                            if len(response.get('CacheClusters', [])) > 0:
                                has_cache = True
                        except ClientError:
                            pass

                        # Also check replication groups
                        if not has_cache:
                            try:
                                response = await client.describe_replication_groups(MaxRecords=1)
                                has_cache = len(response.get('ReplicationGroups', [])) > 0
                            except ClientError:
                                pass

                        logger.debug(f"   • ElastiCache resources in {region}: {has_cache}")
                        return has_cache

                    elif service == 's3':
                        # S3 is global, but we can check if there are any buckets
                        response = await client.list_buckets()
                        has_buckets = len(response.get('Buckets', [])) > 0
                        logger.debug(f"   • S3 buckets (global): {has_buckets}")
                        return has_buckets

                    elif service == 'iam':
                        # IAM is global, always return True for any region
                        logger.debug(f"   • IAM is global service, always included")
                        return True
                    else:
                        # Unknown service, assume no resources
                        logger.debug(f"   • Unknown service {service}, assuming no resources")
                        return False

            # Run the check with timeout
            result = await asyncio.wait_for(check_service(), timeout=self.timeout_seconds)
            return result

        except asyncio.TimeoutError:
            logger.warning(f"⏱️  Timeout checking {service} in {region}, assuming no resources")
            return False
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code in ['UnauthorizedOperation', 'AccessDenied', 'Forbidden']:
                logger.warning(f"🔒 Access denied checking {service} in {region}, assuming no resources")
                return False
            elif error_code in ['InvalidRegion', 'UnsupportedOperation']:
                logger.debug(f"🚫 Service {service} not supported in region {region}")
                return False
            elif error_code in ['ThrottlingException', 'RequestLimitExceeded']:
                logger.warning(f"🚦 Rate limited checking {service} in {region}, assuming no resources")
                return False
            else:
                logger.warning(f"⚠️  AWS error checking {service} in {region}: {error_code}, assuming no resources")
                return False
        except Exception as e:
            logger.warning(f"❌ Unexpected error checking {service} in {region}: {str(e)}, assuming no resources")
            return False
    
    async def is_service_available_in_region(self, service: str, region: str) -> bool:
        """
        Check if a specific service is available in a region.
        
        Args:
            service: Service name
            region: Region to check
            
        Returns:
            True if service is available in the region, False otherwise
        """
        return await self._check_service_in_region(service, region)
