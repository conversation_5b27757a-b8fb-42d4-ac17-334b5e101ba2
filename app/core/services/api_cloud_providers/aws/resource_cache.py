import asyncio
import time
from typing import Dict, List, Optional, Set, Tuple
from celery.utils.log import get_task_logger
from dataclasses import dataclass
from threading import Lock

__all__ = ['ResourceCache', 'CacheEntry']

logger = get_task_logger(__name__)


@dataclass
class CacheEntry:
    """Cache entry for resource presence data."""
    data: Dict
    timestamp: float
    ttl_seconds: int
    
    @property
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        return time.time() - self.timestamp > self.ttl_seconds


class ResourceCache:
    """
    Thread-safe cache for AWS resource presence data.
    Caches Resource Explorer and direct service check results to improve performance.
    """
    
    def __init__(self, default_ttl_seconds: int = 300):  # 5 minutes default TTL
        self.default_ttl_seconds = default_ttl_seconds
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = Lock()
        
    def _generate_cache_key(self, service: str, region: str, account_id: str = None) -> str:
        """Generate a cache key for service-region-account combination."""
        if account_id:
            return f"{account_id}:{service}:{region}"
        return f"{service}:{region}"
    
    def get(self, service: str, region: str, account_id: str = None) -> Optional[bool]:
        """
        Get cached resource presence data.
        
        Args:
            service: AWS service name
            region: AWS region name
            account_id: AWS account ID (optional)
            
        Returns:
            bool: True if resources exist, False if no resources, None if not cached or expired
        """
        cache_key = self._generate_cache_key(service, region, account_id)
        
        with self._lock:
            entry = self._cache.get(cache_key)
            if entry and not entry.is_expired:
                logger.debug(f"Cache hit for {cache_key}: {entry.data.get('has_resources')}")
                return entry.data.get('has_resources')
            elif entry and entry.is_expired:
                logger.debug(f"Cache entry expired for {cache_key}")
                del self._cache[cache_key]
                
        return None
    
    def set(self, service: str, region: str, has_resources: bool, 
            account_id: str = None, ttl_seconds: int = None) -> None:
        """
        Cache resource presence data.
        
        Args:
            service: AWS service name
            region: AWS region name
            has_resources: Whether resources exist in the service-region combination
            account_id: AWS account ID (optional)
            ttl_seconds: Time to live in seconds (optional, uses default if not provided)
        """
        cache_key = self._generate_cache_key(service, region, account_id)
        ttl = ttl_seconds or self.default_ttl_seconds
        
        entry = CacheEntry(
            data={'has_resources': has_resources},
            timestamp=time.time(),
            ttl_seconds=ttl
        )
        
        with self._lock:
            self._cache[cache_key] = entry
            logger.debug(f"Cached result for {cache_key}: {has_resources} (TTL: {ttl}s)")
    
    def get_batch(self, service_region_pairs: List[Tuple[str, str]], 
                  account_id: str = None) -> Dict[Tuple[str, str], Optional[bool]]:
        """
        Get cached data for multiple service-region pairs.
        
        Args:
            service_region_pairs: List of (service, region) tuples
            account_id: AWS account ID (optional)
            
        Returns:
            Dict mapping (service, region) to cached result or None if not cached
        """
        results = {}
        
        for service, region in service_region_pairs:
            cached_result = self.get(service, region, account_id)
            results[(service, region)] = cached_result
            
        return results
    
    def set_batch(self, results: Dict[Tuple[str, str], bool], 
                  account_id: str = None, ttl_seconds: int = None) -> None:
        """
        Cache multiple service-region results.
        
        Args:
            results: Dict mapping (service, region) to resource presence
            account_id: AWS account ID (optional)
            ttl_seconds: Time to live in seconds (optional)
        """
        for (service, region), has_resources in results.items():
            self.set(service, region, has_resources, account_id, ttl_seconds)
    
    def invalidate(self, service: str = None, region: str = None, 
                   account_id: str = None) -> int:
        """
        Invalidate cache entries matching the criteria.
        
        Args:
            service: Service name to invalidate (optional, invalidates all if None)
            region: Region name to invalidate (optional, invalidates all if None)
            account_id: Account ID to invalidate (optional, invalidates all if None)
            
        Returns:
            int: Number of entries invalidated
        """
        invalidated_count = 0
        
        with self._lock:
            keys_to_remove = []
            
            for cache_key in self._cache.keys():
                # Parse cache key to check if it matches criteria
                parts = cache_key.split(':')
                
                if len(parts) == 3:  # account_id:service:region
                    key_account_id, key_service, key_region = parts
                elif len(parts) == 2:  # service:region
                    key_account_id, key_service, key_region = None, parts[0], parts[1]
                else:
                    continue
                
                # Check if entry matches invalidation criteria
                should_invalidate = True
                
                if service and key_service != service:
                    should_invalidate = False
                if region and key_region != region:
                    should_invalidate = False
                if account_id and key_account_id != account_id:
                    should_invalidate = False
                    
                if should_invalidate:
                    keys_to_remove.append(cache_key)
            
            # Remove matching entries
            for key in keys_to_remove:
                del self._cache[key]
                invalidated_count += 1
        
        if invalidated_count > 0:
            logger.info(f"Invalidated {invalidated_count} cache entries")
            
        return invalidated_count
    
    def clear(self) -> int:
        """
        Clear all cache entries.
        
        Returns:
            int: Number of entries cleared
        """
        with self._lock:
            count = len(self._cache)
            self._cache.clear()
            
        if count > 0:
            logger.info(f"Cleared {count} cache entries")
            
        return count
    
    def get_stats(self) -> Dict:
        """
        Get cache statistics.
        
        Returns:
            Dict: Cache statistics including size, expired entries, etc.
        """
        with self._lock:
            total_entries = len(self._cache)
            expired_entries = sum(1 for entry in self._cache.values() if entry.is_expired)
            active_entries = total_entries - expired_entries
            
        return {
            'total_entries': total_entries,
            'active_entries': active_entries,
            'expired_entries': expired_entries,
            'default_ttl_seconds': self.default_ttl_seconds
        }
    
    def cleanup_expired(self) -> int:
        """
        Remove expired entries from cache.
        
        Returns:
            int: Number of expired entries removed
        """
        removed_count = 0
        
        with self._lock:
            keys_to_remove = [
                key for key, entry in self._cache.items() 
                if entry.is_expired
            ]
            
            for key in keys_to_remove:
                del self._cache[key]
                removed_count += 1
        
        if removed_count > 0:
            logger.debug(f"Cleaned up {removed_count} expired cache entries")
            
        return removed_count


# Global cache instance
_global_cache = ResourceCache()


def get_resource_cache() -> ResourceCache:
    """Get the global resource cache instance."""
    return _global_cache
