import asyncio
from typing import Dict, List, Optional, Set
from celery.utils.log import get_task_logger
from botocore.exceptions import ClientError, NoCredentialsError
from app.common.enums import AWSServiceNameEnum, AWSRegionNameEnum
from .aws_service_factory import AWSServiceFactory

__all__ = ['AWSResourceExplorerService']

logger = get_task_logger(__name__)


class AWSResourceExplorerService:
    """
    AWS Resource Explorer service for querying resource presence by service and region.
    Provides intelligent resource discovery to optimize scanning performance.
    """
    
    def __init__(self, credentials: Dict, region_name: str = AWSRegionNameEnum.US_EAST_1.value):
        self.credentials = credentials
        self.region_name = region_name
        self.aws_service_factory = AWSServiceFactory(credentials, region_name)
        self._resource_explorer_available = None
        
    async def is_resource_explorer_available(self) -> bool:
        """
        Check if AWS Resource Explorer is available and enabled in the account.
        
        Returns:
            bool: True if Resource Explorer is available, False otherwise
        """
        if self._resource_explorer_available is not None:
            return self._resource_explorer_available
            
        try:
            session = self.aws_service_factory.get_session()
            async with session.client(AWSServiceNameEnum.RESOURCE_EXPLORER.value, 
                                    region_name=self.region_name) as client:
                # Try to list indexes to check if Resource Explorer is set up
                response = await client.list_indexes()
                indexes = response.get('Indexes', [])
                
                # Resource Explorer is available if there's at least one index
                self._resource_explorer_available = len(indexes) > 0
                
                if self._resource_explorer_available:
                    logger.info("✅ AWS Resource Explorer is available and configured with indexes")
                else:
                    logger.info("⚠️  AWS Resource Explorer is not configured (no indexes found)")
                    
                return self._resource_explorer_available
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code in ['AccessDenied', 'UnauthorizedOperation']:
                logger.warning(f"🔒 No permission to access Resource Explorer: {e}")
            elif error_code == 'ResourceExplorerNotEnabledException':
                logger.info("⚠️  AWS Resource Explorer is not enabled in this account")
            else:
                logger.warning(f"⚠️  Error checking Resource Explorer availability: {e}")
            self._resource_explorer_available = False
            return False
            
        except Exception as e:
            logger.warning(f"Unexpected error checking Resource Explorer: {e}")
            self._resource_explorer_available = False
            return False
    
    async def get_resources_by_service_and_region(self, service_name: str, region: str) -> List[Dict]:
        """
        Query Resource Explorer for resources of a specific service in a specific region.
        
        Args:
            service_name: AWS service name (e.g., 'ec2', 'rds', 's3')
            region: AWS region name (e.g., 'us-east-1')
            
        Returns:
            List[Dict]: List of resources found, empty if none exist
        """
        if not await self.is_resource_explorer_available():
            return []
            
        try:
            session = self.aws_service_factory.get_session()
            async with session.client(AWSServiceNameEnum.RESOURCE_EXPLORER.value,
                                    region_name=self.region_name) as client:
                
                # Build query to find resources for specific service and region
                query = f"service:{service_name} region:{region}"
                
                resources = []
                paginator = client.get_paginator('search')
                
                async for page in paginator.paginate(QueryString=query):
                    resources.extend(page.get('Resources', []))
                
                logger.info(f"Found {len(resources)} {service_name} resources in {region} via Resource Explorer")
                return resources
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code == 'ValidationException':
                logger.warning(f"Invalid query for Resource Explorer: {query}")
            else:
                logger.warning(f"Error querying Resource Explorer for {service_name} in {region}: {e}")
            return []
            
        except Exception as e:
            logger.warning(f"Unexpected error querying Resource Explorer: {e}")
            return []
    
    async def has_resources_in_region(self, service_name: str, region: str) -> bool:
        """
        Check if a service has any resources in a specific region using Resource Explorer.
        
        Args:
            service_name: AWS service name (e.g., 'ec2', 'rds', 's3')
            region: AWS region name (e.g., 'us-east-1')
            
        Returns:
            bool: True if resources exist, False otherwise
        """
        resources = await self.get_resources_by_service_and_region(service_name, region)
        return len(resources) > 0
    
    async def get_regions_with_resources(self, service_name: str, regions: List[str]) -> Set[str]:
        """
        Get all regions that have resources for a specific service.
        
        Args:
            service_name: AWS service name (e.g., 'ec2', 'rds', 's3')
            regions: List of regions to check
            
        Returns:
            Set[str]: Set of regions that have resources for the service
        """
        if not await self.is_resource_explorer_available():
            return set()
            
        regions_with_resources = set()
        
        # Use asyncio.gather for concurrent region checks
        tasks = [
            self.has_resources_in_region(service_name, region) 
            for region in regions
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for region, has_resources in zip(regions, results):
            if isinstance(has_resources, bool) and has_resources:
                regions_with_resources.add(region)
            elif isinstance(has_resources, Exception):
                logger.warning(f"Error checking {service_name} resources in {region}: {has_resources}")
        
        logger.info(f"Service {service_name} has resources in regions: {regions_with_resources}")
        return regions_with_resources
    
    async def batch_check_service_regions(self, service_region_pairs: List[tuple]) -> Dict[tuple, bool]:
        """
        Batch check multiple service-region combinations for resource presence.
        
        Args:
            service_region_pairs: List of (service_name, region) tuples
            
        Returns:
            Dict[tuple, bool]: Mapping of (service, region) to resource presence
        """
        if not await self.is_resource_explorer_available():
            return {pair: False for pair in service_region_pairs}
            
        results = {}
        
        # Create tasks for concurrent checking
        tasks = [
            self.has_resources_in_region(service, region)
            for service, region in service_region_pairs
        ]
        
        # Execute all checks concurrently
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Map results back to service-region pairs
        for (service, region), result in zip(service_region_pairs, check_results):
            if isinstance(result, bool):
                results[(service, region)] = result
            else:
                logger.warning(f"Error checking {service} in {region}: {result}")
                results[(service, region)] = False
        
        return results
