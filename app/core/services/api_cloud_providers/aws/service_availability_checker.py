import asyncio
from typing import Dict, List, Optional, Set, Tuple
from celery.utils.log import get_task_logger
from app.common.enums import AWSRegionNameEnum
from .resource_explorer import AWSResourceExplorerService
from .direct_service_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .resource_cache import get_resource_cache
from .aws_service_factory import AWSServiceFactory

__all__ = ['ServiceAvailabilityChecker']

logger = get_task_logger(__name__)


class ServiceAvailabilityChecker:
    """
    Main service for checking AWS service resource availability across regions.
    Uses AWS Resource Explorer as primary method with fallback to direct service checks.
    Includes caching for performance optimization.
    """
    
    def __init__(self, credentials: Dict, account_id: str = None):
        self.credentials = credentials
        self.account_id = account_id
        self.cache = get_resource_cache()
        
        # Initialize Resource Explorer service
        self.resource_explorer = AWSResourceExplorerService(credentials)
        
        # Initialize direct service checker
        aws_factory = AWSServiceFactory(credentials)
        session = aws_factory.get_session()
        self.direct_checker = DirectServiceChecker(session)
        
        self._resource_explorer_available = None
    
    async def check_service_availability(self, service: str, regions: List[str]) -> Dict[str, bool]:
        """
        Check if a service has resources in the specified regions.
        
        Args:
            service: AWS service name (e.g., 'ec2', 'rds', 's3')
            regions: List of AWS regions to check
            
        Returns:
            Dict mapping region to resource presence (True/False)
        """
        logger.info(f"🔍 Checking {service} resource availability across {len(regions)} regions")
        
        # Check cache first
        cached_results = self.cache.get_batch(
            [(service, region) for region in regions],
            self.account_id
        )

        # Separate cached and uncached regions
        uncached_regions = []
        results = {}
        cache_hits = 0
        cache_misses = 0

        for region in regions:
            cached_result = cached_results.get((service, region))
            if cached_result is not None:
                results[region] = cached_result
                cache_hits += 1
                logger.debug(f"Using cached result for {service} in {region}: {cached_result}")
            else:
                uncached_regions.append(region)
                cache_misses += 1
        
        # Check uncached regions
        if uncached_regions:
            logger.info(f"🔍 Checking {len(uncached_regions)} uncached regions for {service}: {uncached_regions}")

            # Try Resource Explorer first
            if await self._is_resource_explorer_available():
                logger.info(f"✅ Using AWS Resource Explorer for {service} availability check")
                uncached_results = await self._check_with_resource_explorer(service, uncached_regions)
            else:
                logger.info(f"⚠️  AWS Resource Explorer not available, using direct service checks for {service}")
                uncached_results = await self._check_with_direct_service(service, uncached_regions)
            
            # Merge results and cache them
            results.update(uncached_results)
            
            # Cache the new results
            cache_data = {(service, region): has_resources 
                         for region, has_resources in uncached_results.items()}
            self.cache.set_batch(cache_data, self.account_id)
        
        # Log summary
        regions_with_resources = [region for region, has_resources in results.items() if has_resources]
        regions_without_resources = [region for region, has_resources in results.items() if not has_resources]
        
        # Log cache performance
        if cache_hits + cache_misses > 0:
            cache_hit_rate = (cache_hits / (cache_hits + cache_misses)) * 100
            logger.info(f"📊 Cache performance for {service}: {cache_hits} hits, {cache_misses} misses ({cache_hit_rate:.1f}% hit rate)")

        # Log optimization results
        if regions_without_resources:
            savings_percent = (len(regions_without_resources) / len(regions)) * 100
            logger.info(f"✅ {service} optimization results:")
            logger.info(f"   • Regions with resources: {len(regions_with_resources)}/{len(regions)} - {regions_with_resources}")
            logger.info(f"   • Regions without resources: {len(regions_without_resources)} - {regions_without_resources}")
            logger.info(f"   • Potential scan time savings: {savings_percent:.1f}%")
        else:
            logger.info(f"ℹ️  {service}: found resources in all {len(regions)} regions - no optimization possible")

        return results
    
    async def batch_check_services(self, services: List[str], regions: List[str]) -> Dict[str, Dict[str, bool]]:
        """
        Check multiple services across multiple regions efficiently.
        
        Args:
            services: List of AWS service names
            regions: List of AWS regions
            
        Returns:
            Dict mapping service name to dict of region -> resource presence
        """
        logger.info(f"Batch checking {len(services)} services across {len(regions)} regions")
        
        results = {}
        
        # Process services concurrently
        tasks = [
            self.check_service_availability(service, regions)
            for service in services
        ]
        
        service_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Map results back to services
        for service, result in zip(services, service_results):
            if isinstance(result, dict):
                results[service] = result
            else:
                logger.error(f"Error checking {service}: {result}")
                # Fallback to all regions for failed service
                results[service] = {region: True for region in regions}
        
        return results
    
    async def get_optimized_scan_plan(self, services: List[str], regions: List[str]) -> Dict[str, List[str]]:
        """
        Get an optimized scan plan showing which regions to scan for each service.
        
        Args:
            services: List of AWS service names
            regions: List of AWS regions
            
        Returns:
            Dict mapping service name to list of regions that should be scanned
        """
        logger.info(f"Creating optimized scan plan for {len(services)} services across {len(regions)} regions")
        
        # Get availability data for all services
        availability_data = await self.batch_check_services(services, regions)
        
        # Build scan plan
        scan_plan = {}
        total_original_scans = len(services) * len(regions)
        total_optimized_scans = 0
        
        for service, region_availability in availability_data.items():
            # Only include regions where the service has resources
            regions_to_scan = [
                region for region, has_resources in region_availability.items() 
                if has_resources
            ]
            scan_plan[service] = regions_to_scan
            total_optimized_scans += len(regions_to_scan)
        
        # Log optimization summary
        scans_saved = total_original_scans - total_optimized_scans
        savings_percent = (scans_saved / total_original_scans) * 100 if total_original_scans > 0 else 0
        
        logger.info(f"📊 Scan Plan Optimization Summary:")
        logger.info(f"   • Original scans: {total_original_scans}")
        logger.info(f"   • Optimized scans: {total_optimized_scans}")
        logger.info(f"   • Scans saved: {scans_saved}")
        logger.info(f"   • Time savings: {savings_percent:.1f}%")
        
        return scan_plan
    
    async def _is_resource_explorer_available(self) -> bool:
        """Check if Resource Explorer is available (cached)."""
        if self._resource_explorer_available is None:
            self._resource_explorer_available = await self.resource_explorer.is_resource_explorer_available()
        return self._resource_explorer_available
    
    async def _check_with_resource_explorer(self, service: str, regions: List[str]) -> Dict[str, bool]:
        """Check service availability using Resource Explorer."""
        results = {}
        
        try:
            # Use Resource Explorer to get regions with resources
            regions_with_resources = await self.resource_explorer.get_regions_with_resources(service, regions)
            
            # Map results
            for region in regions:
                results[region] = region in regions_with_resources
                
        except Exception as e:
            logger.warning(f"Resource Explorer check failed for {service}: {e}")
            # Fallback to direct service check
            results = await self._check_with_direct_service(service, regions)
        
        return results
    
    async def _check_with_direct_service(self, service: str, regions: List[str]) -> Dict[str, bool]:
        """Check service availability using direct service API calls."""
        results = {}
        
        try:
            # Get service-region mapping using direct checker
            service_region_mapping = await self.direct_checker.get_service_regions_mapping(
                [service], regions, self.account_id
            )
            
            regions_with_resources = set(service_region_mapping.get(service, []))
            
            # Map results
            for region in regions:
                results[region] = region in regions_with_resources
                
        except Exception as e:
            logger.error(f"Direct service check failed for {service}: {e}")
            # Conservative fallback: assume all regions have resources
            results = {region: True for region in regions}
        
        return results
    
    def invalidate_cache(self, service: str = None, region: str = None) -> int:
        """
        Invalidate cached results.
        
        Args:
            service: Service to invalidate (optional, invalidates all if None)
            region: Region to invalidate (optional, invalidates all if None)
            
        Returns:
            int: Number of cache entries invalidated
        """
        return self.cache.invalidate(service, region, self.account_id)
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics."""
        return self.cache.get_stats()
