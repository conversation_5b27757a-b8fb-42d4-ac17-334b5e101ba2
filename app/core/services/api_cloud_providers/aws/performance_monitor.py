import time
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from celery.utils.log import get_task_logger
from threading import Lock

__all__ = ['PerformanceMonitor', 'ScanMetrics']

logger = get_task_logger(__name__)


@dataclass
class ScanMetrics:
    """Metrics for a single scan operation."""
    service_name: str
    account_id: str
    total_regions: int
    scanned_regions: int
    skipped_regions: int
    start_time: float
    end_time: Optional[float] = None
    optimization_method: str = "none"  # "resource_explorer", "direct_check", "none"
    cache_hits: int = 0
    cache_misses: int = 0
    errors: List[str] = field(default_factory=list)
    
    @property
    def duration_seconds(self) -> float:
        """Get scan duration in seconds."""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time
    
    @property
    def time_savings_percent(self) -> float:
        """Calculate time savings percentage."""
        if self.total_regions == 0:
            return 0.0
        return ((self.total_regions - self.scanned_regions) / self.total_regions) * 100
    
    @property
    def cache_hit_rate(self) -> float:
        """Calculate cache hit rate percentage."""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return (self.cache_hits / total_cache_requests) * 100


class PerformanceMonitor:
    """
    Performance monitoring service for region-aware scanning.
    Tracks metrics and provides insights on optimization effectiveness.
    """
    
    def __init__(self):
        self._metrics: Dict[str, ScanMetrics] = {}
        self._lock = Lock()
        self._global_stats = {
            'total_scans': 0,
            'total_regions_saved': 0,
            'total_time_saved_seconds': 0,
            'optimization_methods_used': {
                'resource_explorer': 0,
                'direct_check': 0,
                'none': 0
            }
        }
    
    def start_scan(self, service_name: str, account_id: str, total_regions: int) -> str:
        """
        Start tracking a scan operation.
        
        Args:
            service_name: AWS service name
            account_id: AWS account ID
            total_regions: Total number of regions to potentially scan
            
        Returns:
            str: Unique scan ID for tracking
        """
        scan_id = f"{account_id}:{service_name}:{int(time.time())}"
        
        metrics = ScanMetrics(
            service_name=service_name,
            account_id=account_id,
            total_regions=total_regions,
            scanned_regions=0,
            skipped_regions=0,
            start_time=time.time()
        )
        
        with self._lock:
            self._metrics[scan_id] = metrics
            self._global_stats['total_scans'] += 1
        
        logger.info(f"Started tracking scan {scan_id} for {service_name} across {total_regions} regions")
        return scan_id
    
    def update_scan_progress(self, scan_id: str, scanned_regions: int, 
                           optimization_method: str = None, cache_hits: int = 0, 
                           cache_misses: int = 0) -> None:
        """
        Update scan progress metrics.
        
        Args:
            scan_id: Scan ID from start_scan
            scanned_regions: Number of regions actually scanned
            optimization_method: Method used for optimization
            cache_hits: Number of cache hits
            cache_misses: Number of cache misses
        """
        with self._lock:
            if scan_id not in self._metrics:
                logger.warning(f"Scan ID {scan_id} not found in metrics")
                return
                
            metrics = self._metrics[scan_id]
            metrics.scanned_regions = scanned_regions
            metrics.skipped_regions = metrics.total_regions - scanned_regions
            
            if optimization_method:
                metrics.optimization_method = optimization_method
                
            metrics.cache_hits += cache_hits
            metrics.cache_misses += cache_misses
    
    def finish_scan(self, scan_id: str, errors: List[str] = None) -> ScanMetrics:
        """
        Finish tracking a scan operation and return metrics.
        
        Args:
            scan_id: Scan ID from start_scan
            errors: List of errors encountered during scan
            
        Returns:
            ScanMetrics: Final metrics for the scan
        """
        with self._lock:
            if scan_id not in self._metrics:
                logger.warning(f"Scan ID {scan_id} not found in metrics")
                return None
                
            metrics = self._metrics[scan_id]
            metrics.end_time = time.time()
            
            if errors:
                metrics.errors.extend(errors)
            
            # Update global stats
            self._global_stats['total_regions_saved'] += metrics.skipped_regions
            self._global_stats['optimization_methods_used'][metrics.optimization_method] += 1
            
            # Estimate time saved (assuming 30 seconds per region scan)
            estimated_time_saved = metrics.skipped_regions * 30
            self._global_stats['total_time_saved_seconds'] += estimated_time_saved
        
        # Log scan completion metrics
        self._log_scan_completion(metrics)
        
        return metrics
    
    def get_scan_metrics(self, scan_id: str) -> Optional[ScanMetrics]:
        """Get metrics for a specific scan."""
        with self._lock:
            return self._metrics.get(scan_id)
    
    def get_global_stats(self) -> Dict:
        """Get global performance statistics."""
        with self._lock:
            stats = self._global_stats.copy()
            
            # Add calculated fields
            if stats['total_scans'] > 0:
                stats['average_regions_saved_per_scan'] = stats['total_regions_saved'] / stats['total_scans']
                stats['average_time_saved_per_scan_seconds'] = stats['total_time_saved_seconds'] / stats['total_scans']
            else:
                stats['average_regions_saved_per_scan'] = 0
                stats['average_time_saved_per_scan_seconds'] = 0
            
            # Convert time to human readable format
            stats['total_time_saved_minutes'] = stats['total_time_saved_seconds'] / 60
            stats['total_time_saved_hours'] = stats['total_time_saved_minutes'] / 60
            
            return stats
    
    def get_service_stats(self, service_name: str) -> Dict:
        """Get performance statistics for a specific service."""
        service_metrics = []
        
        with self._lock:
            for metrics in self._metrics.values():
                if metrics.service_name == service_name and metrics.end_time is not None:
                    service_metrics.append(metrics)
        
        if not service_metrics:
            return {'service_name': service_name, 'total_scans': 0}
        
        total_scans = len(service_metrics)
        total_regions_saved = sum(m.skipped_regions for m in service_metrics)
        total_time_saved = sum(m.skipped_regions * 30 for m in service_metrics)  # 30 sec per region
        avg_time_savings_percent = sum(m.time_savings_percent for m in service_metrics) / total_scans
        avg_cache_hit_rate = sum(m.cache_hit_rate for m in service_metrics) / total_scans
        
        return {
            'service_name': service_name,
            'total_scans': total_scans,
            'total_regions_saved': total_regions_saved,
            'total_time_saved_seconds': total_time_saved,
            'total_time_saved_minutes': total_time_saved / 60,
            'average_time_savings_percent': avg_time_savings_percent,
            'average_cache_hit_rate': avg_cache_hit_rate,
            'optimization_methods': {
                method: sum(1 for m in service_metrics if m.optimization_method == method)
                for method in ['resource_explorer', 'direct_check', 'none']
            }
        }
    
    def cleanup_old_metrics(self, max_age_hours: int = 24) -> int:
        """
        Clean up old metrics to prevent memory leaks.
        
        Args:
            max_age_hours: Maximum age of metrics to keep
            
        Returns:
            int: Number of metrics cleaned up
        """
        cutoff_time = time.time() - (max_age_hours * 3600)
        cleaned_count = 0
        
        with self._lock:
            scan_ids_to_remove = [
                scan_id for scan_id, metrics in self._metrics.items()
                if metrics.start_time < cutoff_time
            ]
            
            for scan_id in scan_ids_to_remove:
                del self._metrics[scan_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old scan metrics")
            
        return cleaned_count
    
    def _log_scan_completion(self, metrics: ScanMetrics) -> None:
        """Log scan completion with performance metrics."""
        logger.info(f"📊 Scan completed: {metrics.service_name} (Account: {metrics.account_id})")
        logger.info(f"   • Duration: {metrics.duration_seconds:.1f} seconds")
        logger.info(f"   • Regions scanned: {metrics.scanned_regions}/{metrics.total_regions}")
        logger.info(f"   • Regions skipped: {metrics.skipped_regions}")
        logger.info(f"   • Time savings: {metrics.time_savings_percent:.1f}%")
        logger.info(f"   • Optimization method: {metrics.optimization_method}")
        logger.info(f"   • Cache hit rate: {metrics.cache_hit_rate:.1f}%")
        
        if metrics.errors:
            logger.warning(f"   • Errors encountered: {len(metrics.errors)}")


# Global performance monitor instance
_global_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    return _global_monitor
