from app.core.services.api_cloud_providers import AWSServiceFactory
from ..service_availability_checker import ServiceAvailability<PERSON>hecker
from ..performance_monitor import get_performance_monitor
from celery.utils.log import get_task_logger

__all__ = ["BaseChecksProcessor"]

logger = get_task_logger(__name__)


class BaseChecksProcessor:
    def __init__(self, credentials, regions=None):
        if regions is None:
            regions = []
        self.credentials = credentials
        self.client = None
        self.regions = regions
        self.aws_service_factory = None
        self.service_availability_checker = None
        self.optimized_regions = None
        self.account_id = credentials.get('aws_account_id')

    def get_session(self, region_name=None):
        self.aws_service_factory = AWSServiceFactory(self.credentials, region_name)
        return self.aws_service_factory.get_session()

    async def is_region_accessible(self):
        return await self.aws_service_factory.is_region_accessible()

    async def get_optimized_regions(self, service_name: str) -> list:
        """
        Get optimized list of regions that have resources for this service.
        Uses AWS Resource Explorer with fallback to direct service checks.

        Args:
            service_name: AWS service name (e.g., 'ec2', 'rds', 's3')

        Returns:
            List of regions that have resources for the service
        """
        if self.optimized_regions is not None:
            return self.optimized_regions

        # Initialize service availability checker if not already done
        if self.service_availability_checker is None:
            self.service_availability_checker = ServiceAvailabilityChecker(
                self.credentials, self.account_id
            )

        try:
            # Get regions with resources for this service
            region_availability = await self.service_availability_checker.check_service_availability(
                service_name, self.regions
            )

            # Filter to only regions with resources
            self.optimized_regions = [
                region for region, has_resources in region_availability.items()
                if has_resources
            ]

            logger.info(f"Region optimization for {service_name}: {len(self.optimized_regions)}/{len(self.regions)} regions have resources")

            return self.optimized_regions

        except Exception as e:
            logger.warning(f"Failed to optimize regions for {service_name}: {e}")
            # Fallback to all regions if optimization fails
            self.optimized_regions = self.regions
            return self.optimized_regions

    async def run_checks_with_optimization(self, service_name: str):
        """
        Base method for running checks with region optimization.
        This method should be called by service-specific run_checks methods.

        Args:
            service_name: AWS service name for optimization

        Returns:
            Findings dictionary
        """
        # Start performance monitoring
        monitor = get_performance_monitor()
        scan_id = monitor.start_scan(service_name, self.account_id or "unknown", len(self.regions))

        errors = []
        optimization_method = "none"

        try:
            # Get optimized regions (only regions with resources)
            optimized_regions = await self.get_optimized_regions(service_name)

            # Determine optimization method used
            if hasattr(self.service_availability_checker, '_resource_explorer_available'):
                if self.service_availability_checker._resource_explorer_available:
                    optimization_method = "resource_explorer"
                else:
                    optimization_method = "direct_check"

            if not optimized_regions:
                logger.info(f"No regions with {service_name} resources found. Skipping all {service_name} scans.")
                monitor.update_scan_progress(scan_id, 0, optimization_method)
                return getattr(self, 'findings', {})

            logger.info(f"Scanning {service_name} in {len(optimized_regions)} optimized regions: {optimized_regions}")

            # Update progress with optimization info
            monitor.update_scan_progress(scan_id, len(optimized_regions), optimization_method)

            # Run checks only in optimized regions
            for region in optimized_regions:
                logger.info(f"Scanning {service_name} in region: {region}")
                session = self.get_session(region)
                if not await self.is_region_accessible():
                    logger.warning(f"Region {region} is not accessible. Skipping checks.")
                    continue

                try:
                    # This method should be overridden by each service to implement specific scanning logic
                    await self.scan_region(region, session)
                except Exception as e:
                    error_msg = f"Error scanning {service_name} in {region}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            return getattr(self, 'findings', {})

        except Exception as e:
            error_msg = f"Error during {service_name} optimization: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

            # Fallback to scanning all regions
            logger.warning(f"Falling back to scanning all regions for {service_name}")
            for region in self.regions:
                try:
                    session = self.get_session(region)
                    if await self.is_region_accessible():
                        await self.scan_region(region, session)
                except Exception as region_error:
                    error_msg = f"Error scanning {service_name} in {region}: {str(region_error)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            monitor.update_scan_progress(scan_id, len(self.regions), "none")
            return getattr(self, 'findings', {})

        finally:
            # Finish performance monitoring
            monitor.finish_scan(scan_id, errors)

    async def scan_region(self, region: str, session):
        """
        Override this method in service-specific classes to implement region scanning logic.

        Args:
            region: AWS region to scan
            session: AWS session for the region
        """
        raise NotImplementedError("Service-specific classes must implement scan_region method")

    async def get_filtered_regions(self, service_name: str = None):
        """
        Get filtered regions that have resources for the service.
        This method provides backward compatibility with existing implementations.

        Args:
            service_name: AWS service name (auto-detected if not provided)

        Returns:
            List of regions that have resources for the service
        """
        if service_name is None:
            # Try to auto-detect service name from class name or module
            class_name = self.__class__.__name__.lower()
            if 'ec2' in class_name:
                service_name = 'ec2'
            elif 'rds' in class_name:
                service_name = 'rds'
            elif 'lambda' in class_name:
                service_name = 'lambda'
            elif 'ecs' in class_name:
                service_name = 'ecs'
            elif 'eks' in class_name:
                service_name = 'eks'
            elif 'elb' in class_name:
                service_name = 'elb'
            elif 'elasticache' in class_name:
                service_name = 'elasticache'
            elif 'efs' in class_name:
                service_name = 'efs'
            elif 's3' in class_name:
                service_name = 's3'
            elif 'iam' in class_name:
                service_name = 'iam'
            else:
                # Fallback to module name detection
                module_name = self.__module__.lower()
                for service in ['ec2', 'rds', 'lambda', 'ecs', 'eks', 'elb', 'elasticache', 'efs', 's3', 'iam']:
                    if service in module_name:
                        service_name = service
                        break

        if service_name is None:
            logger.warning("Could not auto-detect service name, using all regions")
            return self.regions

        return await self.get_optimized_regions(service_name)

