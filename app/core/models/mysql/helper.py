import aiomysql


__all__ = ['fetch_row', 'fetch_rows', 'insert', 'insert_many', 'insert_row', 'update_row', 'delete', 'delete_rows']


async def fetch_row(conn_pool, sql_stmt, params=None):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            if params:
                await cursor.execute(sql_stmt, params)
            else:
                await cursor.execute(sql_stmt)
            result = await cursor.fetchone()
            return result


async def fetch_rows(conn_pool, sql_stmt, params=None):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            if params:
                await cursor.execute(sql_stmt, params)
            else:
                await cursor.execute(sql_stmt)
            result = await cursor.fetchall()
            return result


async def insert(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result


async def insert_many(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.executemany(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result


async def insert_row(conn, sql_stmt, params):
    result = {
        "insert_id": None,
        "rowcount": None
    }
    async with conn.cursor() as cursor:
        await cursor.execute(sql_stmt, params)
        result['insert_id'] = cursor.lastrowid
        result['rowcount'] = cursor.rowcount
    return result


async def update_row(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result


async def delete(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result


async def delete_rows(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.executemany(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result
