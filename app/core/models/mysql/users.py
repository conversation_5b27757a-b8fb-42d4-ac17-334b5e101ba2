from .helper import fetch_row, insert, fetch_rows, update_row
from ...models import sql_scripts

__all__ = ['get_user_data', 'add_new_user', 'delete_user', 'fetch_permissions',
           'fetch_user_permissions', 'get_user_by_id', 'add_workspace', 'update_workspace_user', 'update_user_admin',
           'get_workspace_name', 'get_team_members', 'update_user_password', 'update_user_info',
           'get_workspace_owner', 'transfer_workspace_ownership', 'update_new_owner_admin_status']


async def get_user_data(conn_pool, message):
    """Fetch data from users table"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_user_data'], params={"email": message.email})


async def add_new_user(conn_pool, email, password_hash, workspace_id, created_at, created_by=None, first_name=None, last_name=None):
    """Insert data into users table"""
    result = await insert(conn_pool, sql_stmt=sql_scripts['add_new_user'],
                          params={"email": email, "password_hash": password_hash, "workspace_id": workspace_id,
                                  "created_by": created_by, "created_at": created_at, 
                                  "first_name": first_name, "last_name": last_name})
    return result['id']


async def delete_user(conn_pool, user_id):
    """Delete user from users table"""
    return await insert(conn_pool, sql_stmt=sql_scripts['delete_user'], params={"user_id": user_id})





async def fetch_permissions(conn_pool):
    """Fetch permissions from permissions table"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['fetch_permissions'])


async def fetch_user_permissions(conn_pool, user_id):
    """Fetch permissions for a user"""
    result = await fetch_rows(conn_pool, sql_stmt=sql_scripts['fetch_user_permissions'], params={"user_id": user_id})
    return [row['name'] for row in result]


async def get_user_by_id(conn_pool, user_id):
    """Fetch user by id"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_user_by_id'], params={"user_id": user_id})


async def add_workspace(conn_pool, name, created_at, created_by=None):
    """Insert data into workspace table"""
    result = await insert(conn_pool, sql_stmt=sql_scripts['add_workspace'],
                          params={"name": name, "created_at": created_at, "created_by": created_by})
    return result['id']


async def update_workspace_user(conn_pool, user_id, workspace_id):
    """Update user in workspace table"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['update_workspace_user'],
                            params={"created_by": user_id, "workspace_id": workspace_id})


async def update_user_admin(conn_pool, user_id):
    """Update user in workspace table"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['update_user_admin'],
                            params={"user_id": user_id})


async def get_workspace_name(conn_pool, workspace_id):
    """Fetch workspace name by id"""
    response = await fetch_row(conn_pool, sql_stmt=sql_scripts['get_workspace_by_id'], params={"workspace_id": workspace_id})
    return response["name"]


async def get_team_members(conn_pool, workspace_id):
    """Fetch all users in the same workspace"""
    return await fetch_rows(
        conn_pool,
        sql_stmt=sql_scripts['get_team_members'],
        params={"workspace_id": workspace_id}
    )


async def update_user_password(conn_pool, user_id, password_hash):
    """Update user password in users table"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['update_user_password'],
                            params={"user_id": user_id, "password_hash": password_hash})


async def update_user_info(conn_pool, user_id, updates):
    """Update user information"""
    # Build dynamic SQL based on what fields are being updated
    set_clauses = []
    params = {"user_id": user_id}

    for key, value in updates.items():
        set_clauses.append(f"{key} = %({key})s")
        params[key] = value

    set_clause = ", ".join(set_clauses)
    sql = sql_scripts['update_user_info'].replace("#set_clause#", set_clause)

    return await update_row(conn_pool, sql_stmt=sql, params=params)


async def get_workspace_owner(conn_pool, workspace_id):
    """Get the current workspace owner"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_workspace_owner'],
                          params={"workspace_id": workspace_id})


async def transfer_workspace_ownership(conn_pool, workspace_id, current_owner_user_id, new_owner_user_id):
    """Transfer workspace ownership from current owner to new owner"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['transfer_workspace_ownership'],
                           params={"workspace_id": workspace_id,
                                  "current_owner_user_id": current_owner_user_id,
                                  "new_owner_user_id": new_owner_user_id})


async def update_new_owner_admin_status(conn_pool, user_id):
    """Update new owner's admin status (self-reference in created_by)"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['update_new_owner_admin_status'],
                           params={"user_id": user_id})
