import json
from .helper import fetch_row, insert, update_row, fetch_rows
from ...models import sql_scripts
from app.common import get_utc_timestamp, ScanStatusEnum, ScanServiceStatusEnum


__all__ = [
    "check_active_running_scans",
    "check_scan_from_account",
    "create_scan",
    "add_scan_service",
    "get_account_credentials",
    "save_findings",
    "check_scan_completion",
    "update_scan",
    "update_scan_service",
    "get_findings_for_scan_service_policy_check",
    "update_findings",
    "get_scan_service",
    "update_scan_service_status_from_scan_and_service",
    "get_user_scans",
    "get_user_scans_count",
    "get_scan_detail",
    "get_scan_services",
    "sync_scan_services"
]


async def check_active_running_scans(conn_pool, message, user_id):
    """Fetch data from accounts and scans tables to check if user is allowed to run more scans"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["check_running_account_scans"],
        params={"user_id": user_id, "account_id": message.account_id},
    )
    return result["active_running_accounts"], result["given_account_running_scans"]


# check and fetch scan id for account id
async def check_scan_from_account(conn_pool, message):
    """Fetch scan id for the given account id"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["check_scan_from_account"],
        params={"account_id": message.account_id},
    )
    return result["id"] if result else None


async def create_scan(conn_pool, message):
    """Create a new scan and make entry in scans table with status as 'running'"""
    curr_time = get_utc_timestamp()
    result = await insert(
        conn_pool,
        sql_stmt=sql_scripts["create_scan"],
        params={"account_id": message.account_id, "scan_start": curr_time},
    )
    return result["id"]


async def get_account_credentials(conn_pool, account_id):
    """Fetch account credentials from accounts table"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_account_credentials"],
        params={"account_id": account_id},
    )
    return json.loads(result["credential_data"]) if result else {}


async def get_findings_for_scan_service_policy_check(conn_pool, scan_id, service_id, policy_check):
    """Fetch findings for the given scan service id and policy check"""
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_findings_for_scan_service_policy_check"],
        params={"scan_id": scan_id, "service_id": service_id, "policy_check": policy_check},
    )


async def update_findings(conn_pool, finding_id, finding_detail):
    """Update findings for the given scan service id and policy check"""
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts["update_findings"],
        params={
            "finding_id": finding_id,
            "status": finding_detail["status"],
            "details": json.dumps(finding_detail["details"]),
        },
    )


async def save_findings(conn_pool, scan_id, service_id, policy_check, finding_detail):
    """Add entry in findings table with status pass/fail and severity level"""
    return await insert(
        conn_pool,
        sql_stmt=sql_scripts["save_findings"],
        params={
            "scan_id": scan_id,
            "service_id": service_id,
            "policy_check": policy_check,
            "severity": finding_detail["severity"],
            "description": finding_detail["description"],
            "status": finding_detail["status"],
            "details": json.dumps(finding_detail["details"]),
            "created_at": get_utc_timestamp(),
        },
    )


async def check_scan_completion(conn_pool, scan_id):
    """Check if all user selected services with given scan_id are completed"""
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["check_scan_completion"],
        params={"scan_id": scan_id},
    )


async def update_scan(conn_pool, scan_id, status=ScanStatusEnum.COMPLETED.value, update_start=False, update_end=False):
    """Update the scan status and optionally update scan_start or scan_end timestamps"""
    params = {"status": status, "scan_id": scan_id}

    update_start_stmt = update_end_stmt = ""
    if update_start:
        update_start_stmt = ", scan_start = %(scan_start)s, scan_end = NULL"
        params["scan_start"] = get_utc_timestamp()

    if update_end:
        update_end_stmt = ", scan_end = %(scan_end)s"
        params["scan_end"] = get_utc_timestamp()

    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts["update_scan"].replace("#update_start_stmt#", update_start_stmt).replace("#update_end_stmt#", update_end_stmt),
        params=params,
    )


async def add_scan_service(conn_pool, scan_id, service_id):
    """Add entry in scan_services table with status as 'running'"""
    result = await insert(
        conn_pool,
        sql_stmt=sql_scripts["add_scan_service"],
        params={"scan_id": scan_id, "service_id": service_id},
    )
    return result["id"]


async def update_scan_service(conn_pool, scan_service_id, status=ScanStatusEnum.COMPLETED.value, update_last_scanned_at=False):
    """Update the scan service status to 'completed'"""
    params = {"status": status, "scan_service_id": scan_service_id}
    update_last_scan = ''
    if update_last_scanned_at:
        update_last_scan = ', last_scanned_at = %(last_scanned_at)s'
        params["last_scanned_at"] = get_utc_timestamp()

    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts["update_scan_service"].replace("#update_last_scan#", update_last_scan),
        params=params,
    )

async def get_scan_service(conn_pool, scan_id, service_id):
    """Fetch scan service details for the given scan id and service id"""
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_scan_service"],
        params={"scan_id": scan_id, "service_id": service_id},
    )

async def update_scan_service_status_from_scan_and_service(conn_pool, scan_id, service_id, status=ScanStatusEnum.RUNNING.value):
    """Update the scan service status to 'completed'"""
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts["update_scan_service_status_from_scan_and_service"],
        params={"status": status, "scan_id": scan_id, "service_id": service_id},
    )

async def get_user_scans_count(conn_pool, user_id):
    """Get the total count of scans for a specific user"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_user_scans_count"],
        params={"user_id": user_id},
    )
    return result["count"] if result else 0

async def get_user_scans(conn_pool, user_id, page=1, page_size=10):
    """Fetch all scans for a specific user with pagination"""
    offset = (page - 1) * page_size
    results = await fetch_rows(
        conn_pool,
        sql_stmt=sql_scripts["get_user_scans"],
        params={"user_id": user_id, "limit": page_size, "offset": offset},
    )
    return results

async def get_scan_detail(conn_pool, scan_id, user_id):
    """Get detailed information about a specific scan"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_scan_detail"],
        params={"scan_id": scan_id, "user_id": user_id},
    )
    return result

async def get_scan_services(conn_pool, scan_id):
    """Get all services associated with a scan"""
    results = await fetch_rows(
        conn_pool,
        sql_stmt=sql_scripts["get_scan_services"],
        params={"scan_id": scan_id},
    )
    return results


async def sync_scan_services(conn_pool, scan_id, service_id, service_status=ScanServiceStatusEnum.PENDING.value):
    scan_service = await get_scan_service(conn_pool, scan_id, service_id)
    scan_service_id = scan_service['id'] if scan_service else None
    if scan_service_id:
        await update_scan_service(conn_pool, scan_service_id, service_status)
    else:
        scan_service_id = await add_scan_service(conn_pool, scan_id, service_id)
    return scan_service_id