import os
from distutils.util import strtobool

from pydantic_settings import BaseSettings

__all__ = ["Configurations"]


class Configurations(BaseSettings):
    """Environment Variable configuration"""

    def __init__(self):
        super().__init__()
        self.app_config()
        self.auth_config()
        self.logging_config()
        self.mysql_config()
        self.rabbitmq_config()
        self.celery_config()
        self.utility_config()
        self.slack_config()

    @classmethod
    def app_config(cls):
        """Basic Application level configurations"""

        cls.SERVICE_NAME = os.environ.get("SERVICE_NAME", "cloudaudit-backend-service")
        cls.PRODUCT = os.environ.get("PRODUCT", "CloudAudit")
        cls.ENVIRONMENT = os.environ.get("ENVIRONMENT")
        cls.DEBUG = strtobool(os.environ.get("DEBUG", "0"))
        cls.SERVICE_PORT = int(os.environ.get("SERVICE_PORT", 8000))
        cls.LOG_LEVEL = "INFO" if not cls.DEBUG else "DEBUG"

    @classmethod
    def auth_config(cls):
        """Authentication configurations"""

        cls.JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY")
        cls.ENCRYPTION_ALGORITHM = os.environ.get("ENCRYPTION_ALGORITHM", "HS256")
        cls.ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30))
        cls.REFRESH_TOKEN_EXPIRE_DAYS = int(os.environ.get("REFRESH_TOKEN_EXPIRE_DAYS", 7))

    @classmethod
    def logging_config(cls):
        """ " Logger configurations"""

        cls.ENABLE_THIRD_PARTY_LOGGING = int(os.environ.get("ENABLE_THIRD_PARTY_LOGGING", 0))

    @classmethod
    def mysql_config(cls):
        """MySQL configurations"""

        cls.MYSQL_DATABASE_HOST = os.environ.get("MYSQL_DATABASE_HOST", "localhost")
        cls.MYSQL_DATABASE_NAME = os.environ.get("MYSQL_DATABASE_NAME", "cloudaudit")
        cls.MYSQL_DATABASE_USER = os.environ.get("MYSQL_DATABASE_USER", "root")
        cls.MYSQL_DATABASE_PASSWORD = os.environ.get("MYSQL_DATABASE_PASSWORD", "admin")

        cls.MYSQL_CONNECTION_POOL_MINIMUM_SIZE = int(os.environ.get("MYSQL_CONNECTION_POOL_MINIMUM_SIZE", 5))
        cls.MYSQL_CONNECTION_POOL_MAXIMUM_SIZE = int(os.environ.get("MYSQL_CONNECTION_POOL_MAXIMUM_SIZE", 10))
        cls.MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME = int(os.environ.get("MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME", 10))

    @classmethod
    def rabbitmq_config(cls):
        """RabbitMQ Configurations"""
        cls.RABBIT_MQ_HOST = os.environ.get("RABBIT_MQ_HOST", "localhost")
        cls.RABBIT_MQ_HOST_URL = os.environ.get("RABBIT_MQ_HOST_URL", "amqp://localhost")
        cls.RABBIT_MQ_PORT = int(os.environ.get("RABBIT_MQ_PORT", 5672))
        cls.RABBIT_MQ_V_HOST = os.environ.get("RABBIT_MQ_V_HOST", "%2f")
        cls.RABBIT_MQ_USERNAME = os.environ.get("RABBIT_MQ_USERNAME", "guest")
        cls.RABBIT_MQ_PASSWORD = os.environ.get("RABBIT_MQ_PASSWORD", "guest")
        cls.RABBIT_MQ_HEARTBEAT = os.environ.get("RABBIT_MQ_HEARTBEAT", 60)

        cls.RABBITMQ_CONNECTION_POOL_MAX_SIZE = int(os.environ.get("RABBITMQ_CONNECTION_POOL_MAX_SIZE", "100"))
        cls.RABBITMQ_CHANNEL_POOL_MAX_SIZE = int(os.environ.get("RABBITMQ_CHANNEL_POOL_MAX_SIZE", "1024"))
        cls.RABBIT_MQ_PREFETCH_COUNT = int(os.environ.get("RABBIT_MQ_PREFETCH_COUNT", 1))

        """Queue Workers Configurations"""
        # cls.SCAN_STATUS_UPDATE_HANDLER_QUEUE_WORKER_COUNT = int(
        #     os.environ.get("SCAN_STATUS_UPDATE_HANDLER_QUEUE_WORKER_COUNT", 5))

        """Queue Prefetch Count Configurations"""
        # cls.SCAN_STATUS_UPDATE_HANDLER_QUEUE_PREFETCH_COUNT = int(
        #     os.environ.get("SCAN_STATUS_UPDATE_HANDLER_QUEUE_PREFETCH_COUNT", 10))

    @classmethod
    def celery_config(cls):
        """Celery Configurations"""
        cls.CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL", "pyamqp://guest:guest@localhost:5672//")
        cls.CELERY_RESULT_BACKEND = os.environ.get("CELERY_RESULT_BACKEND", "redis://localhost:6379/0")

    @classmethod
    def utility_config(cls):
        """Utility Configurations"""
        cls.MAX_SCAN_ACCOUNTS = int(os.environ.get("MAX_SCAN_ACCOUNTS", 5))
        cls.AWS_ASSUME_ROLE_SESSION_DURATION = int(os.environ.get("AWS_ASSUME_ROLE_SESSION_DURATION", 21600))

    @classmethod
    def slack_config(cls):
        """Slack Bot Configurations"""
        cls.SLACK_BOT_TOKEN = os.environ.get("SLACK_BOT_TOKEN")
        cls.SLACK_CHANNEL_ID = os.environ.get("SLACK_CHANNEL_ID")